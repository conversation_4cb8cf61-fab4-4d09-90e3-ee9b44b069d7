<template>
  <div class="flex flex-col">
    <com-bread :data="breadData"></com-bread>
    <div class="item_content notVw flex-1">
      <UnitSelect class="unit-tree" style="min-width: 323px" @updateVal="updateVal" v-show="showTree" />
      <div style="flex: 6; position: relative" :style="{ width: showTree ? '62vw' : '100%' }">
        <radio-tab class="tab-container" :tab-list="tabList" :tab="tabType" @change="handleTabChange" />
        <div class="right-table" :style="`height: calc(100% - ${toVw(40)})`">
          <img
            src="@/assets/select_tree.png"
            class="select-tree"
            :style="{ width: toVw(30), left: `-${toVw(15)}` }"
            v-show="userInfo.unitOrgType == '2'"
            @click="showTree = !showTree"
            alt=""
          />
          <CustomPointTab
            v-show="tabType == '2'"
            ref="customPointTabRef"
            :unitId="filterVal.unitId"
            :unitName="unitName"
            :isBusinessUnit="isBusinessUnit"
            @action="actionFn"
          />
          <RiskPointTab
            v-show="tabType == '1'"
            ref="riskPointTabRef"
            :unitId="filterVal.unitId"
            :unitName="unitName"
            :isBusinessUnit="isBusinessUnit"
            @action="actionFn"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watchEffect, nextTick } from 'vue';
import UnitSelect from '@/components/unitSelect/index.vue';
import ComBread from '@/components/breadcrumb/ComBread.vue';
import RadioTab from '@/components/tab/ComRadioTabE.vue';
import CustomPointTab from './comp/tab1/index.vue';
import RiskPointTab from './comp/tab2/index.vue';
import { tabList } from './constant';
import { useStore } from '@/store';
import { toVw } from '@/utils/fit';
import { useRoute, useRouter } from 'vue-router';

const { userInfo } = useStore();
const route = useRoute();
const router = useRouter();
const breadData = [{ name: '隐患治理系统' }, { name: '巡查点位管理' }];
const customPointTabRef = ref();
const riskPointTabRef = ref();
let filterVal = ref({ unitId: userInfo.unitId });
const isBusinessUnit = ref(); //true:业务单位 false:非业务单位
const unitName = ref(); //选中的树节点name
const showTree = ref(true);
const tabType = ref<string>((route.query?.tabs as string) || '1');
// 初始化判断当前用户是否是业务单位
isBusinessUnit.value = userInfo.unitOrgType == '1';

const reset = () => {
  // 重置当前tab的查询
  if (tabType.value === '2') {
    customPointTabRef.value?.resetQuery();
  } else {
    riskPointTabRef.value?.resetQuery();
  }
};
// tab切换
const handleTabChange = (val: string) => {
  router.push({ query: { tabs: val } });
  reset();
  tabType.value = val;
  handleSearch();
};

// 组织结构切换
function updateVal(unitId: any, _isBusinessUnit: boolean, { treeName }: any) {
  reset();
  isBusinessUnit.value = _isBusinessUnit || false;
  unitName.value = treeName;
  filterVal.value = { unitId };
}

watchEffect(() => {
  if (filterVal.value.unitId) {
    console.log('当前选择的单位id', filterVal.value.unitId);
  }
});

function actionFn(val: any) {
  // 处理来自子组件的action事件
  console.log('action from child:', val);
}

function handleSearch() {
  if (tabType.value === '2') {
    customPointTabRef.value?.handleSearch(filterVal.value);
  } else {
    riskPointTabRef.value?.handleSearch(filterVal.value);
  }
}

defineOptions({ name: 'inspection_point' });
</script>

<style module lang="scss"></style>
