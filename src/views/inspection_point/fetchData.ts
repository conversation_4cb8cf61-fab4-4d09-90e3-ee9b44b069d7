import { IObj } from '@/types';
import { api } from '@/api';
import { $http } from '@tanzerfe/http';

// 列表
export function getCheckPointList(query: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.ww.paramsConf.getCheckPointList);
  return $http.post<any[]>(url, {
    data: { _cfg: { showTip: true }, ...query },
  });
}

// 巡查点位-新增/编辑
export function saveOrUpdateCheckPoint(query: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.ww.paramsConf.saveOrUpdateCheckPoint);
  return $http.post<any[]>(url, {
    data: { ...query },
  });
}

// 巡查点位-详情
export function getCheckPointDetail(query: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.ww.paramsConf.getCheckPointDetail, query);
  return $http.post<any[]>(url, {
    data: { _cfg: { showTip: true } },
  });
}

// 检查表列表
export function getCheckList(data: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.sh.checkList.listPage);
  return $http.post(url, {
    data: { _cfg: { showTip: true }, ...data },
  });
}

// 查询编制单位
export function getOrgCodeUpList(query: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.ww.paramsConf.getOrgCodeUpList, query);
  return $http.post(url, {
    data: { _cfg: { showTip: true } },
  });
}

export function getUpAndDownUnitInfo(data: IObj<any>) {
  const url = api.getUrl(api.type.hazard, '/hazardCheckTable/getUpAndDownUnitInfo', { ...data });
  return $http.post(url, {
    data: { _cfg: { showTip: true } },
  });
}

// 检查表-检查内容详情
export function getCheckTableDetail(id: any) {
  const url = api.getUrl(api.type.hazard, api.ww.paramsConf.getCheckTableDetail, { id });
  return $http.post(url, {
    data: { _cfg: { showTip: true } },
  });
}

// 删除
export function deleteCheckPointById(query: { id: string }) {
  const url = api.getUrl(api.type.hazard, api.ww.paramsConf.deleteCheckPointById, query);
  return $http.post(url, {
    data: {
      _cfg: { showTip: true, showOkTip: true, okTipContent: '删除成功' },
    },
  });
}

// 获取楼栋楼层树
export function getBuildingTreeByUnitId(data: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.ww.paramsConf.getBuildingTreeByUnitId, data);
  return $http.post(url, {
    data: { _cfg: { showTip: false } },
  });
}

// 获取单位
export function getAllUnit(query: {
  orgType: number;
  orgCode: string;
  pageNo: number;
  pageSize: number;
  type?: string;
}) {
  const url = api.getUrl(api.type.hazard, api.public.getAllUnit);
  return $http.post(url, {
    data: { _cfg: { showTip: true }, ...query },
  });
}

// 批量导出二维码
export function exportBatchQrCode() {
  return api.getUrl(api.type.hazard, api.ww.paramsConf.exportBatchQrCode);
}

// 批量关联检查表
export function bindCheckTableByBatch(data: any) {
  const url = api.getUrl(api.type.hazard, api.ww.paramsConf.bindCheckTableByBatch);
  return $http.post(url, {
    data: { _cfg: { showTip: true, showOkTip: true }, ...data },
  });
}

// 获取设备管理分类tab
export function getDeviceMainTypePageList(data: any) {
  const url = api.getUrl('ehs-clnt-device-service', '/deviceType/deviceMainTypePageList');
  return $http.post(url, {
    data: { _cfg: { showTip: true }, ...data },
  });
}

// 获取设备管理table数据
export function getDevicePageList(data: any, zhId: any) {
  const url = api.getUrl('ehs-clnt-device-service', '/device/pageList');
  return $http.post(url, {
    data: { _cfg: { showTip: true }, ...data },
  });
}

// 获取设备类型
export function getDeviceTypeList(data: any) {
  const url = api.getUrl('ehs-clnt-device-service', '/deviceType/queryDeviceTypeTreeById', data);
  return $http.get<any[]>(url, { data: { _cfg: { showTip: true } } });
}

// 获取标签枚举
export function getTagList(data: any) {
  const url = api.getUrl('ehs-clnt-device-service', '/erecordDeviceTag/pageList', data);
  return $http.post(url, {
    data: { _cfg: { showTip: true } },
  });
}

// 获取设备状态枚举
export function queryDictDataByType(data: any) {
  const url = api.getUrl('ehs-clnt-device-service', '/dict/queryDictDataByType', data);
  return $http.get<any[]>(url, { data: { _cfg: { showTip: true } } });
}

// 巡查点位-从设备/重点部位导入
export function importCheckPointByBatch(data: any) {
  const url = api.getUrl(api.type.hazard, api.ww.paramsConf.importCheckPointByBatch);
  return $http.post(url, {
    data: { _cfg: { showTip: true, showOkTip: true }, ...data },
  });
}

// 重点部位列表查询
export function keyPartManageList(data: any, zhId: any) {
  // zhId == 'hbzdn' ? 'ehs-clnt-device-service-wh' : 'ehs-clnt-device-service'
  const url = api.getUrl('ehs-clnt-device-service', '/keyPartManage/pageList');
  return $http.post(url, {
    data: { _cfg: { showTip: true }, ...data },
  });
}

// 下载模版
export function getTemplateUrl(data: any) {
  const url = api.getUrl(api.type.hazard, api.ww.paramsConf.exportTemplateForYjy, data);
  return url;
}

// 导入模版
export function getActionUrl() {
  const url = api.getUrl(api.type.hazard, api.ww.paramsConf.importForYjy);
  return url;
}

// 批量导出
export function exportPointManage() {
  const url = api.getUrl(api.type.hazard, api.ww.paramsConf.exportPointManage);
  return url;
}

// 批量删除
export function deleteBatchApi(data: any) {
  const url = api.getUrl(api.type.hazard, api.ww.paramsConf.deleteBatch, data);
  return $http.post(url, {
    data: { _cfg: { showTip: true, showOkTip: true } },
  });
}

// 导出点位
export function exportPointCodeRule() {
  const url = api.getUrl(api.type.hazard, '/hazardCheckPointCodeRule/exportPointCodeRule');
  return url;
}

// 更新点位
export function addPointCodeRule(data: any) {
  const url = api.getUrl(api.type.hazard, 'hazardCheckPointCodeRule/addPointCodeRule');
  return $http.post(url, {
    data: { _cfg: { showTip: false, showOkTip: false }, ...data },
  });
}

// 动态获取风险点位搜索框
export function getRiskSearchInput(data: any) {
  const url = api.getUrl(api.type.hazard, api.sct.risk.riskEva, data);
  return $http.post<any[]>(url, {
    data: { _cfg: { showTip: true } },
  });
}

// 风险点位搜索下拉字典
export function getRiskKeyList(data: any) {
  const url = api.getUrl(api.type.hazard, api.sct.risk.riskKey, data);
  return $http.post<any[]>(url, {
    data: { _cfg: { showTip: true } },
  });
}

// 获取风险点位列表
export function getRiskPointList(query: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.sct.risk.riskPoint);
  return $http.post<any[]>(url, {
    data: { _cfg: { showTip: true }, ...query },
  });
}

// 导出风险二维码
export function exportRiskQrCode() {
  return api.getUrl(api.type.hazard, api.sct.risk.exportErcode);
}

// 风险点位获取历史隐患等级
export function getRiskHistoryHLevel(query: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.sct.risk.hazardLevelMerge);
  return $http.post<any[]>(url, {
    data: { _cfg: { showTip: true }, ...query },
  });
}

// 风险点位获取历史隐患整改
export function getRiskHistoryHReact(query: IObj<any>) {
  const url = api.getUrl(api.type.hazard, api.sct.risk.hazardReactMerge);
  return $http.post<any[]>(url, {
    data: { _cfg: { showTip: true }, ...query },
  });
}
