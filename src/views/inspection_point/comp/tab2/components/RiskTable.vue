<template>
  <div class="table-container">
    <n-data-table
      class="h-full com-table com-table-container !p-[0]"
      remote
      striped
      :columns="columns"
      :data="tableData"
      :bordered="false"
      :flex-height="true"
      :row-key="(row: any) => row.id"
      :loading="loading"
      :pagination="pagination"
      :scroll-x="1200"
      @update:checked-row-keys="handleCheck"
    />

    <!-- 风险点位详情侧边栏 -->
    <ComDrawerA
      :title="'风险点位详情'"
      :show="showDetailModal"
      :maskNeedClosable="true"
      :footerPaddingBottom="25"
      class="!w-[600px]"
      @handleNegative="handleCloseDetail"
    >
      <RiskPointDetail v-if="showDetailModal" :data="currentRowData" @close="handleCloseDetail" />
    </ComDrawerA>
    <!-- 新建编辑巡查点位 + 单独配置检查表 -->
    <FormDialog
      ref="formDialogRef"
      @success="resetQuery"
      :id="id"
      :checkTableId="checkTableId"
      :formDialogType="formDialogType"
      :unitId="props.unitId"
    />
  </div>
</template>

<script setup lang="ts">
import { h, ref, onMounted } from 'vue';
import { DataTableColumns, NButton, useMessage, NImage } from 'naive-ui';
import { useNaivePagination } from '@/common/hooks/useNaivePagination';
import RiskPointDetail from './RiskPointDetail.vue';
import getFileURL from '@/utils/getFileURL';
import FormDialog from '../../../components/FormDialog.vue';

interface Props {
  unitId: string;
  unitName: string;
}

const props = defineProps<Props>();
const emits = defineEmits(['action']);

const message = useMessage();
const tableData = ref<any[]>([]);
const loading = ref(false);
const checkedRowKeys = ref<any[]>([]);
const showDetailModal = ref(false);
const currentRowData = ref<any>({});
const formType = ref(1); // 1: 新增, 2: 编辑
const id = ref('');
const checkTableId = ref('');
const formDialogRef = ref();
const formDialogType = ref<any>(1); //1新增  2编辑  3详情

// 表格列配置
const columns: DataTableColumns = [
  {
    type: 'selection',
    width: 50,
  },
  {
    title: '风险点编码',
    key: 'riskPointCode',
    width: 120,
    resizable: true,
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '风险点名称',
    key: 'riskPointName',
    width: 150,
    resizable: true,
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '风险点位置',
    key: 'riskPointPosition',
    width: 100,
    resizable: true,
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '风险点等级',
    key: 'areaName',
    width: 120,
    resizable: true,
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '管控层级',
    key: 'controlLeveName',
    width: 100,
    resizable: true,
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '管控责任人',
    key: 'controlResponsible',
    width: 120,
  },
  {
    title: '检查表名称',
    key: 'checkTableName',
    align: 'left',
    width: 170,
    resizable: true,
    ellipsis: {
      tooltip: true,
    },
    render: (row: any) => {
      return h(
        'span',
        {
          style: {
            color: '#409eff',
          },
          class: 'cursor-pointer',
          onClick: () => {
            singleConfigCheckTable({ _id: row.id, _checkTableId: row.checkTableId });
          },
        },
        row.checkTableName ? row.checkTableName : '去配置>'
      );
    },
  },
  {
    title: '点位二维码',
    key: 'qrCodeUrl',
    width: 120,
    ellipsis: {
      tooltip: true,
    },
    render(row: any) {
      let img = h(
        NImage,
        {
          style: 'width: 50px; height: 30px',
          src: getFileURL(row.qrCodeUrl),
        },
        () => row.hazardGradeName
      );
      return row.qrCodeUrl ? img : '--';
    },
  },
  {
    title: '操作',
    key: 'actions',
    width: 200,
    fixed: 'right',
    render: (row: any) => {
      return h('div', { class: 'flex gap-2' }, [
        h(
          NButton,
          {
            size: 'small',
            type: 'primary',
            ghost: true,
            onClick: () => handleDetail(row),
          },
          { default: () => '详情' }
        ),
        h(
          NButton,
          {
            size: 'small',
            type: 'error',
            ghost: true,
            onClick: () => handleHistory(row),
          },
          { default: () => '历史隐患' }
        ),
      ]);
    },
  },
];

// 模拟数据
const mockData = [
  {
    id: '1',
    riskPointCode: 'FX001',
    riskPointName: '危险化学品储存区',
    riskLevel: '1',
    areaName: '生产区A',
    controlStatus: '1',
    responsiblePerson: '张三',
    createTime: '2024-01-15 10:30:00',
  },
];

// 获取表格数据
const getTableDataWrap = async (params?: any) => {
  loading.value = true;
  try {
    // TODO: 调用实际API
    // const res = await getRiskPointList({
    //   ...params,
    //   pageNo: pagination.page,
    //   pageSize: pagination.pageSize,
    // });

    // 模拟API调用
    await new Promise((resolve) => setTimeout(resolve, 500));
    tableData.value = mockData;
    pagination.itemCount = mockData.length;
  } catch (error) {
    message.error('获取数据失败');
  } finally {
    loading.value = false;
  }
};
const { pagination } = useNaivePagination(getTableDataWrap);

// 处理选择
const handleCheck = (keys: any[]) => {
  checkedRowKeys.value = keys;
};

// 查看详情
const handleDetail = (row: any) => {
  currentRowData.value = row;
  showDetailModal.value = true;
};

// 关闭详情弹框
const handleCloseDetail = () => {
  showDetailModal.value = false;
  currentRowData.value = {};
};

// 查看历史隐患
const handleHistory = (row: any) => {};

// 单独配置检查表
const singleConfigCheckTable = ({ _id, _checkTableId }: any) => {
  formDialogType.value = null;
  checkTableId.value = _checkTableId;
  id.value = _id;
  formDialogRef.value.showCheckTableDialog();
};

// 删除
const handleDelete = (row: any) => {
  // TODO: 实现删除逻辑
  message.info('删除功能开发中');
};

// 表单操作成功
const handleFormSuccess = () => {
  getTableDataWrap();
  message.success(formType.value === 1 ? '新增成功' : '编辑成功');
};

// 重置查询
const resetQuery = () => {
  pagination.page = 1;
  getTableDataWrap();
};

// 批量下载二维码
const downloadErCodes = () => {
  // TODO: 实现风险点位批量下载二维码功能
  message.info('批量下载二维码功能开发中');
};

// 暴露方法
defineExpose({
  getTableDataWrap,
  resetQuery,
  downloadErCodes,
  pagination,
});

onMounted(() => {
  getTableDataWrap();
});

defineOptions({ name: 'RiskTable' });
</script>

<style scoped lang="scss">
.table-container {
  background: #fff;
  border-radius: 8px;
  height: 100%;
  display: flex;
  flex-direction: column;
}
</style>
