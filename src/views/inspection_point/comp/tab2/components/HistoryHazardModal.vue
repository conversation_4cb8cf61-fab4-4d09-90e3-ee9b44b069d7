<template>
  <n-modal
    v-model:show="showModal"
    :mask-closable="false"
    preset="card"
    style="width: 90%; height: 800px; background-color: #eef7ff"
    title="历史隐患"
  >
    <div class="history-hazard-content">
      <!-- 卡片 -->
      <statistic ref="statisticRef" />
      <!-- 表格区域 -->
      <div class="table-container mt-4">
        <n-data-table
          class="com-table"
          style="height: 100%"
          remote
          striped
          :columns="columns"
          :data="tableData"
          :bordered="false"
          :flex-height="true"
          :loading="loading"
          :pagination="pagination"
          :render-cell="useEmptyCell"
          :scroll-x="2040"
        />
      </div>
    </div>
  </n-modal>
</template>

<script setup lang="ts">
import { ref, onMounted, h, VNode, nextTick } from 'vue';
import { NButton, SelectOption, useMessage } from 'naive-ui';
import statistic from '@/views/hazard-management/comp/statistic.vue';
import { useAutoLoading } from '@/common/hooks/useAutoLoading';
import { useNaivePagination } from '@/common/hooks/useNaivePagination';
import { useActionDivider } from '@/common/hooks/useActionDivider';
import { useEmptyCell } from '@/common/hooks/useEmptyCell';
import { useStore } from '@/store';
import {
  mergePageEvent,
  getHazardGradeList,
  getHazardOverdueList,
  getHazardInventoryDetail,
} from '@/views/hazard-management/fetchData';
import { cols } from '@/views/hazard-management/comp/table/columns';
import { IDisposeState, IHazardRowInfo } from '@/views/hazard-management/type';

interface Props {
  riskPointId?: string;
}

const props = defineProps<Props>();
const emits = defineEmits(['close']);

const message = useMessage();
const { userInfo } = useStore();

const showModal = ref(false);
const statisticRef = ref();
const [loading, search] = useAutoLoading(true);
const { pagination, updateTotal } = useNaivePagination(getTableData);

const tableData = ref<any[]>([]);
const hazardLevelList = ref<SelectOption[]>([]);
const hazardOverdueOptions = ref<SelectOption[]>([]);

// 表格列配置
const columns = ref([
  ...cols,
  {
    title: '操作',
    key: 'actions',
    align: 'left',
    fixed: 'right',
    width: 170,
    render: (row: any) => getActionBtn(row),
  },
]);

function getActionBtn(row: IHazardRowInfo | any) {
  const acList: [VNode, boolean?][] = [
    [
      h(
        NButton,
        {
          size: 'small',
          type: 'primary',
          ghost: true,
          class: 'com-action-button',
          onClick: () => handleShowDetails(row),
        },
        { default: () => '详情' }
      ),
    ],
    [
      h(
        NButton,
        {
          size: 'small',
          type: 'primary',
          ghost: true,
          class: 'com-action-button',
          disabled: row.disposeState !== IDisposeState.待整改,
          onClick: () => handleHazardUrge(row),
        },
        { default: () => '催促' }
      ),
    ],
  ];
  return useActionDivider(acList);
}

function getTableData(data: any) {
  const paramsData = {
    pageNo: pagination.page,
    pageSize: pagination.pageSize,
    roleCodes: userInfo.roleCodes,
    riskPointId: data.riskPointId,
    unitId: data.unitId,
  };

  search(mergePageEvent(paramsData)).then((res) => {
    tableData.value = res.data.rows || [];
    updateTotal(res.data.total || 0);
  });
}

const handleShowDetails = (row: any) => {
  getHazardInventoryDetail({ id: row.id }).then((res) => {
    if (res.data) {
      // 这里可以打开详情弹框，暂时用消息提示
      message.info('隐患详情功能待实现');
    }
  });
};

const handleHazardUrge = (row: any) => {
  // 催促功能
  message.info('催促功能待实现');
};

// 获取筛选数据
const getFilterFormData = async () => {
  getHazardGradeList({ delFlag: 0, unitId: userInfo.topUnitId }).then((res) => {
    hazardLevelList.value = res.data.map((item) => ({
      label: item.gradeName,
      value: item.id,
    }));
  });

  getHazardOverdueList({
    pageSize: -1,
    pageNo: 1,
    unitId: userInfo.topUnitId,
  }).then((res) => {
    hazardOverdueOptions.value = res.data.rows.map((item) => ({
      label: '超期' + item.overdueDay + '天',
      value: item.overdueDay,
    }));
    hazardOverdueOptions.value.unshift({ label: '否', value: 0 });
  });
};

// 打开弹框
const open = (data: any) => {
  showModal.value = true;
  getFilterFormData();
  getTableData(data);
  // TODO 传参待修改
  nextTick(() => {
    statisticRef.value.getDataToRisk({ riskPointId: data.riskPointId, unitId: data.unitId });
  });
};

defineExpose({
  open,
});

defineOptions({ name: 'HistoryHazardModal' });
</script>

<style scoped lang="scss">
.history-hazard-content {
  height: 100%;
  background-color: #eef7ff;
}

.filter-box {
  @apply flex flex-row items-start;
  .collapse-btn {
    @apply w-[70px] mt-[4px] text-[18px] cursor-pointer flex flex-nowrap items-center gap-[8px];
  }
}

.table-container {
  height: 80%;
  background-color: #eef7ff;
  border-radius: 8px;
}

:deep(.n-date-picker) {
  width: 100%;
}
</style>
