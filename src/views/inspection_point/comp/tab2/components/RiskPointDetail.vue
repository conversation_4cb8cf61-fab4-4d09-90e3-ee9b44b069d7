<template>
  <div class="risk-point-detail">
    <div class="detail-grid">
      <div class="detail-item">
        <div class="label">风险点编码：</div>
        <div class="value">{{ data.riskPointCode || '--' }}</div>
      </div>

      <div class="detail-item">
        <div class="label">风险点名称：</div>
        <div class="value">{{ data.riskPointName || '--' }}</div>
      </div>

      <div class="detail-item">
        <div class="label">风险点位置：</div>
        <div class="value">{{ data.riskPointPosition || '--' }}</div>
      </div>

      <div class="detail-item">
        <div class="label">管控层级：</div>
        <div class="value">{{ data.controlLeveName || '--' }}</div>
      </div>

      <div class="detail-item">
        <div class="label">管控责任人：</div>
        <div class="value">{{ data.controlResponsible || '--' }}</div>
      </div>

      <div class="detail-item">
        <div class="label">检查表名称：</div>
        <div class="value">{{ data.checkTableName || '--' }}</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  data: any;
}

const props = defineProps<Props>();
const emits = defineEmits(['close']);

// 获取管控状态标签
const getControlStatusLabel = (status: string) => {
  const statusMap: Record<string, string> = {
    '1': '正常管控',
    '2': '异常',
    '3': '停用',
  };
  return statusMap[status] || '--';
};

// 获取管控状态颜色
const getControlStatusColor = (status: string) => {
  const colorMap: Record<string, string> = {
    '1': 'success',
    '2': 'error',
    '3': 'default',
  };
  return colorMap[status] || 'default';
};

defineOptions({ name: 'RiskPointDetail' });
</script>

<style scoped lang="scss">
.risk-point-detail {
  padding: 34px 40px;
  background: #fff;
  border-radius: 8px;
}

.detail-grid {
  display: grid;
  gap: 40px;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 16px;
  }
}

.detail-item {
  display: flex;
  gap: 8px;

  .label {
    font-size: 14px;
    font-weight: 500;
    color: #666;
    line-height: 1.4;
  }

  .value {
    font-size: 14px;
    color: #333;
    line-height: 1.5;
    word-break: break-all;

    &:empty::after {
      content: '--';
      color: #999;
    }
  }
}

// 针对较长内容的项目，让其占据整行
.detail-item:nth-child(2),  // 风险点名称
.detail-item:nth-child(6) {
  // 检查表名称
  grid-column: 1 / -1;
}
</style>
