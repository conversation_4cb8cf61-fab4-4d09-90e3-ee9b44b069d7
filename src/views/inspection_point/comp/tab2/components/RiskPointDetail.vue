<template>
  <div class="risk-point-detail">
    <n-descriptions :column="1" label-placement="left">
      <n-descriptions-item label="风险点编码">
        {{ data.riskPointCode || '--' }}
      </n-descriptions-item>
      <n-descriptions-item label="风险点名称">
        {{ data.riskPointName || '--' }}
      </n-descriptions-item>
      <n-descriptions-item label="风险点位置">
        {{ data.riskPointPosition || '--' }}
      </n-descriptions-item>
      <n-descriptions-item label="管控层级">
        <n-tag v-if="data.controlStatus" :type="getControlStatusColor(data.controlStatus)">
          {{ getControlStatusLabel(data.controlStatus) }}
        </n-tag>
        <span v-else>--</span>
      </n-descriptions-item>
      <n-descriptions-item label="管控责任人">
        {{ data.responsiblePerson || '--' }}
      </n-descriptions-item>
      <n-descriptions-item label="检查表名称">
        {{ data.checkTableName || '--' }}
      </n-descriptions-item>
    </n-descriptions>
  </div>
</template>

<script setup lang="ts">
interface Props {
  data: any;
}

const props = defineProps<Props>();
const emits = defineEmits(['close']);

// 获取风险等级标签
const getRiskLevelLabel = (level: string) => {
  const levelMap: Record<string, string> = {
    '1': '重大风险',
    '2': '较大风险',
    '3': '一般风险',
    '4': '低风险',
  };
  return levelMap[level] || '--';
};

// 获取风险等级颜色
const getRiskLevelColor = (level: string) => {
  const colorMap: Record<string, string> = {
    '1': 'error',
    '2': 'warning',
    '3': 'info',
    '4': 'success',
  };
  return colorMap[level] || 'default';
};

// 获取管控状态标签
const getControlStatusLabel = (status: string) => {
  const statusMap: Record<string, string> = {
    '1': '正常管控',
    '2': '异常',
    '3': '停用',
  };
  return statusMap[status] || '--';
};

// 获取管控状态颜色
const getControlStatusColor = (status: string) => {
  const colorMap: Record<string, string> = {
    '1': 'success',
    '2': 'error',
    '3': 'default',
  };
  return colorMap[status] || 'default';
};

const handleClose = () => {
  emits('close');
};

defineOptions({ name: 'RiskPointDetail' });
</script>

<style scoped lang="scss">
.risk-point-detail {
  padding: 20px;
}
</style>
