<template>
  <n-collapse :on-item-header-click="collapseClick" :trigger-areas="['main', 'arrow']" arrow-placement="right">
    <n-form :show-feedback="false" label-placement="left" label-width="120px" ref="ruleFormRef">
      <n-collapse-item>
        <template #header>
          <n-button text type="primary" width="120">
            {{ collapse ? '收起' : '展开' }}
          </n-button>
        </template>
        <template #header-extra>
          <n-grid :x-gap="12" :y-gap="8" :cols="3">
            <n-gi>
              <n-form-item label="风险点等级:">
                <n-select v-model:value="form.riskLevel" placeholder="全部" :options="riskLevelOptions" clearable />
              </n-form-item>
            </n-gi>
            <n-gi>
              <n-form-item label="管控层级:">
                <n-select
                  v-model:value="form.controlLevel"
                  placeholder="全部"
                  :options="controlStatusOptions"
                  clearable
                />
              </n-form-item>
            </n-gi>
            <n-gi v-if="collapse">
              <n-form-item>
                <n-input
                  placeholder="请输入风险点编码/名称/位置/管控责任人模糊搜索"
                  v-model:value="form.keyword"
                  maxlength="50"
                  show-word-limit
                  clearable
                >
                  <template #suffix>
                    <BsSearch />
                  </template>
                </n-input>
              </n-form-item>
            </n-gi>
            <n-gi v-if="!collapse">
              <div class="flex justify-end">
                <el-dropdown split-button type="primary">
                  批量操作
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item>
                        <el-button class="min-w-[190px]" type="primary" plain @click="emits('downloadErCodes')"
                          >批量下载二维码</el-button
                        >
                      </el-dropdown-item>
                      <el-dropdown-item>
                        <el-button class="min-w-[190px]" type="primary" plain @click="emits('updateErCodes')"
                          >更新点位二维码</el-button
                        >
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>
            </n-gi>
          </n-grid>
        </template>
        <n-grid :x-gap="24" :y-gap="8" :cols="3" v-if="collapse">
          <n-gi :span="12">
            <div class="flex justify-end">
              <el-dropdown split-button type="primary">
                批量操作
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item>
                      <el-button class="min-w-[190px]" type="primary" plain @click="emits('downloadErCodes')"
                        >批量下载二维码</el-button
                      >
                    </el-dropdown-item>
                    <el-dropdown-item>
                      <el-button class="min-w-[190px]" type="primary" plain @click="emits('updateErCodes')"
                        >更新点位二维码</el-button
                      >
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </n-gi>
        </n-grid>
      </n-collapse-item>
    </n-form>
  </n-collapse>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import { ACTION } from '../../../constant';
import type { FormInstance } from 'element-plus';
import { BsSearch } from '@kalimahapps/vue-icons';
import { throttle } from 'lodash-es';

const emits = defineEmits(['action', 'downloadErCodes', 'updateErCodes']);

const form = ref<any>({
  riskLevel: null, // 风险等级
  controlLevel: null, // 管控层级
  keyword: null, // 关键词
});

// 风险等级选项
const riskLevelOptions = [
  { label: '重大风险', value: '1' },
  { label: '较大风险', value: '2' },
  { label: '一般风险', value: '3' },
  { label: '低风险', value: '4' },
];

// 管控层级选项
const controlStatusOptions = [
  { label: '集团级', value: '1' },
  { label: '公司级', value: '2' },
  { label: '部门级', value: '3' },
];

const collapse = ref(false);
const collapseClick = (e: any) => {
  collapse.value = e.expanded;
};

function getVal() {
  return form.value;
}

function getValFool() {
  // 兼容原有逻辑
  return;
}

const clearFrom = (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  formEl.resetFields();
};

const doHandle = throttle(() => {
  emits('action', {
    action: ACTION.SEARCH,
    data: form.value,
  });
}, 1000);

watch(form.value, () => {
  doHandle();
});

defineOptions({ name: 'RiskFilter' });
defineExpose({
  getVal,
  getValFool,
  clearFrom,
});
</script>

<style lang="scss" scoped>
:deep(.el-form-item) {
  margin-bottom: 0;
}

:deep(.n-collapse-item__header-extra) {
  width: calc(100% - 60px);
}

:deep(.n-collapse-item__content-inner) {
  padding-left: 60px;
}

.n-date-picker {
  width: 100%;
}
.ml_12px {
  margin-left: 12px;
}
</style>
