<template>
  <n-collapse :on-item-header-click="collapseClick" :trigger-areas="['main', 'arrow']" arrow-placement="right">
    <n-form :show-feedback="false" label-placement="left" label-width="120px">
      <n-collapse-item>
        <template #header>
          <n-button text type="primary" width="120">
            {{ collapse ? '收起' : '展开' }}
          </n-button>
        </template>
        <template #header-extra>
          <n-grid :x-gap="12" :y-gap="8" :cols="3">
            <n-gi>
              <n-form-item :label="`${inherentRisk}:`">
                <n-select
                  v-model:value="form.inherentRisk"
                  placeholder="请选择"
                  label-field="value"
                  value-field="key"
                  :options="riskLevelOptions"
                  clearable
                />
              </n-form-item>
            </n-gi>
            <n-gi>
              <n-form-item :label="`${currentRisk}:`">
                <n-select
                  v-model:value="form.currentRisk"
                  placeholder="请选择"
                  label-field="value"
                  value-field="key"
                  :options="riskLevelOptions"
                  clearable
                />
              </n-form-item>
            </n-gi>
            <n-gi v-if="collapse">
              <n-form-item>
                <n-input
                  placeholder="请输入风险点编码/名称/位置/管控责任人模糊搜索"
                  v-model:value="form.keyword"
                  maxlength="50"
                  show-word-limit
                  clearable
                >
                  <template #suffix>
                    <BsSearch />
                  </template>
                </n-input>
              </n-form-item>
            </n-gi>
            <n-gi v-if="!collapse">
              <div class="flex justify-end">
                <el-dropdown split-button type="primary">
                  批量操作
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item>
                        <el-button class="min-w-[190px]" type="primary" plain @click="emits('downloadErCodes')"
                          >批量下载二维码</el-button
                        >
                      </el-dropdown-item>
                      <el-dropdown-item>
                        <el-button class="min-w-[190px]" type="primary" plain @click="emits('updateErCodes')"
                          >更新点位二维码</el-button
                        >
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>
            </n-gi>
          </n-grid>
        </template>
        <n-grid :x-gap="12" :y-gap="8" :cols="3" v-if="collapse">
          <n-gi>
            <n-form-item label="管控层级:">
              <n-select
                v-model:value="form.controlLevel"
                placeholder="请选择"
                label-field="value"
                value-field="key"
                :options="controlStatusOptions"
                clearable
              />
            </n-form-item>
          </n-gi>
          <n-gi :span="2">
            <div class="flex justify-end">
              <el-dropdown split-button type="primary">
                批量操作
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item>
                      <el-button class="min-w-[190px]" type="primary" plain @click="emits('downloadErCodes')"
                        >批量下载二维码</el-button
                      >
                    </el-dropdown-item>
                    <el-dropdown-item>
                      <el-button class="min-w-[190px]" type="primary" plain @click="emits('updateErCodes')"
                        >更新点位二维码</el-button
                      >
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </n-gi>
        </n-grid>
      </n-collapse-item>
    </n-form>
  </n-collapse>
</template>

<script setup lang="ts">
import { ref, watch, onMounted } from 'vue';
import { ACTION } from '../../../constant';
import { BsSearch } from '@kalimahapps/vue-icons';
import { throttle } from 'lodash-es';
import { getRiskSearchInput, getRiskKeyList } from '../../../fetchData';
import { useStore } from '@/store';

const userInfo = useStore()?.userInfo;
const emits = defineEmits(['action', 'downloadErCodes', 'updateErCodes']);
const inherentRisk = ref<string>('固有风险等级');
const currentRisk = ref<string>('现状风险等级');

const form = ref<any>({
  inherentRisk: null, // 固有风险
  currentRisk: null, // 现状风险
  controlLevel: null, // 管控层级
  keyword: '', // 关键词
});

// 风险等级选项
const riskLevelOptions = ref<{ key: string; value: string }[]>([]);

// 管控层级选项
const controlStatusOptions = ref<{ key: string; value: string }[]>([]);

const collapse = ref(false);
const collapseClick = (e: any) => {
  collapse.value = e.expanded;
};

function getVal() {
  return form.value;
}

function getValFool() {
  // 兼容原有逻辑
  return;
}

const clearFrom = () => {
  form.value.inherentRisk = null;
  form.value.controlLevel = null;
  form.value.currentRisk = null;
  form.value.keyWord = '';
};

const doHandle = throttle(() => {
  emits('action', {
    action: ACTION.SEARCH,
    data: form.value,
  });
}, 1000);

const getRiskInput = async () => {
  const res = await getRiskSearchInput({ unitId: userInfo.topUnitId });
  inherentRisk.value = res.data[0].value || '固有风险等级';
  currentRisk.value = res.data[1].value || '现状风险等级';
};

const getRiskKey = async (type: string) => {
  const res: any = await getRiskKeyList({ unitId: userInfo.topUnitId, type });
  console.log('res', res);
  type == '1' ? (riskLevelOptions.value = res.data) : (controlStatusOptions.value = res.data);
};

watch(form.value, () => {
  doHandle();
});

onMounted(async () => {
  await getRiskInput();
  await getRiskKey('1');
  await getRiskKey('2');
});

defineOptions({ name: 'RiskFilter' });
defineExpose({
  getVal,
  getValFool,
  clearFrom,
});
</script>

<style lang="scss" scoped>
:deep(.el-form-item) {
  margin-bottom: 0;
}

:deep(.n-collapse-item__header-extra) {
  width: calc(100% - 60px);
}

:deep(.n-collapse-item__content-inner) {
  padding-left: 60px;
}

.n-date-picker {
  width: 100%;
}
.ml_12px {
  margin-left: 12px;
}
</style>
