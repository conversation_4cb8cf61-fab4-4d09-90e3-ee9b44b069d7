<template>
  <div class="risk-point-tab">
    <RiskFilter
      @action="actionFn"
      ref="filterRef"
      @downloadErCodes="downloadErCodes()"
      @updateErCodes="updateErCodes()"
    />
    <RiskTable class="mt-4" ref="tableCompRef" @action="actionFn" :unitId="filterVal.unitId" :unitName="unitName" />
  </div>
</template>

<script setup lang="ts">
import { ref, watchEffect } from 'vue';
import RiskFilter from './components/RiskFilter.vue';
import RiskTable from './components/RiskTable.vue';
import { ACTION } from '../../constant';
import { useStore } from '@/store';

interface Props {
  unitId: string;
  unitName: string;
  isBusinessUnit: boolean;
}

const props = defineProps<Props>();
const emits = defineEmits(['action']);

const { userInfo } = useStore();
const currentAction = ref({ action: ACTION.ADD, data: {} });
const filterRef = ref();
const tableCompRef = ref();
let filterVal = ref({ unitId: props.unitId });

// 监听props变化
watchEffect(() => {
  filterVal.value.unitId = props.unitId;
});

function actionFn(val: any) {
  currentAction.value = val;
  if (val.action === ACTION.SEARCH) {
    filterVal.value = { unitId: filterVal.value.unitId, ...val.data };
    return handleSearch(filterVal.value);
  }
  emits('action', val);
}

// 批量下载二维码
const downloadErCodes = () => {
  tableCompRef.value?.downloadErCodes();
};

// 批量更新点位二维码
const updateErCodes = () => {};

function handleSearch(data: any) {
  tableCompRef.value?.getTableDataWrap(data);
}

// 重置查询
const resetQuery = () => {
  filterRef.value?.clearFrom();
  filterVal.value = { unitId: props.unitId };
  tableCompRef.value.pagination.page = 1;
  filterRef.value?.getValFool();
  handleSearch(filterVal.value);
};

// 暴露方法给父组件
defineExpose({
  resetQuery,
  handleSearch,
});

defineOptions({ name: 'RiskPointTab' });
</script>

<style scoped lang="scss">
.risk-point-tab {
  height: 100%;
  display: flex;
  flex-direction: column;
}
</style>
